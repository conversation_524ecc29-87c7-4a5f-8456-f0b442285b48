on: [push, pull_request]
name: GoBeansDB Test
jobs:
  test:
    strategy:
      matrix:
        compiler: [gcc]
        go-version: [1.12.x, 1.13.x]
        platform: [ubuntu-latest, macos-latest]
    runs-on: ${{ matrix.platform }}
    steps:
    - name: Install Go
      uses: actions/setup-go@v1
      with:
        go-version: ${{ matrix.go-version }}

    - name: Checkout code
      uses: actions/checkout@v1

    - name: Get test tools
      run: go get -u -v golang.org/x/tools/cmd/goimports
    
    - name: Test
      env:
        CC: ${{ matrix.compiler }}
      run: |
        export PATH=${PATH}:`go env GOPATH`/bin
        diff <(goimports -d .) <(printf "")
        go mod vendor
        make test

    - name: Install
      run: make install
