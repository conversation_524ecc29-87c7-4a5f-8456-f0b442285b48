# BeansDB Java

A Java implementation of BeansDB, a distributed key-value storage system compatible with the memcached protocol.

## Overview

This is a Java port of the core functionality from [GoBeansDB](https://github.com/douban/gobeansdb), implementing the essential features of the distributed key-value storage system:

- **Memcached Protocol Compatibility**: Full compatibility with memcached clients
- **Log-Structured Storage**: Data files are append-only logs with 256-byte alignment
- **In-Memory Indexing**: Complete key indexing in memory using HTree (16-ary Merkle tree)
- **Hints System**: Fast lookup optimization with collision handling
- **Bucket-Based Sharding**: Static hash routing with configurable bucket count
- **Data Integrity**: CRC32 checksums and structured record format

## Features Implemented

### Core Storage Engine
- **HStore**: Main storage engine managing multiple buckets
- **Bucket**: Individual bucket management with data and index
- **DataStore**: Data file management with chunking and rotation
- **HTree**: 16-ary Merkle tree for in-memory indexing
- **HintManager**: Fast lookup system with collision handling
- **CollisionTable**: Hash collision resolution for hints

### Data Structures
- **Record**: Key-value records with metadata
- **Payload**: Data payload with compression support detection
- **Position**: Data location tracking (chunk ID + offset)
- **KeyInfo**: Key information with hash and bucket routing
- **HintItem**: Fast lookup items for collision handling

### Protocol Support
- **MemcacheServer**: Simple memcached protocol server
- **StorageClient**: Interface between protocol and storage engine
- Basic memcached commands: GET, SET, DELETE, INCR, DECR, STATS

### Utilities
- **HashUtils**: FNV-1a hashing and key validation
- **CRC32 Support**: Data integrity checking
- **Key Validation**: Memcached protocol compliance

## Architecture

```
┌─────────────────┐
│ Memcache Server │  ← TCP Server (Port 7900)
└─────────┬───────┘
          │
┌─────────▼───────┐
│ Storage Client  │  ← Protocol Interface
└─────────┬───────┘
          │
┌─────────▼───────┐
│     HStore      │  ← Main Storage Engine
└─────────┬───────┘
          │
┌─────────▼───────┐
│    Buckets      │  ← Bucket Management (256 buckets)
└─────────┬───────┘
          │
┌─────────▼───────┐    ┌─────────────┐    ┌─────────────┐
│   DataStore     │────│    HTree    │────│ HintManager │
│  (Data Files)   │    │  (Index)    │    │  (Hints)    │
└─────────────────┘    └─────────────┘    └─────────────┘
```

## Hints System

The hints system is a key optimization that provides faster lookups and handles hash collisions:

### How Hints Work
1. **Fast Lookup**: Hints provide a direct path to data without traversing the HTree
2. **Collision Handling**: When multiple keys have the same hash, hints help distinguish them
3. **Dual Index**: Both HTree and hints are updated on writes, but reads prefer hints

### Lookup Priority
```
GET request → Check Hints → If not found → Check HTree → Read from DataStore
```

### Benefits
- **Performance**: Faster lookups by avoiding HTree traversal
- **Collision Resolution**: Proper handling of hash collisions
- **Consistency**: Maintains consistency between hints and HTree

## Quick Start

### Build

```bash
cd beansdb-java
mvn clean compile
```

### Run Tests

```bash
mvn test
```

### Start Server

```bash
# Using Maven
mvn exec:java -Dexec.mainClass="com.buzz.beansdb.BeansDBServer"

# Or compile and run directly
mvn compile exec:java -Dexec.mainClass="com.buzz.beansdb.BeansDBServer" -Dexec.args="-p 7900 -d /tmp/beansdb"
```

### Command Line Options

```bash
java -jar beansdb-java.jar [options]

Options:
  -h, --help              Show help message
  -p, --port <port>       Port to listen on (default: 7900)
  -d, --home <directory>  Home directory for data storage (default: /tmp/beansdb)
  -b, --buckets <number>  Number of buckets (must be power of 2, default: 256)
```

## Usage Examples

### Using with memcached clients

```python
import memcache

# Connect to BeansDB
mc = memcache.Client(['localhost:7900'])

# Basic operations
mc.set("key1", "value1")
value = mc.get("key1")
print(value)  # "value1"

# Delete
mc.delete("key1")

# Increment
mc.set("counter", "0")
mc.incr("counter", 5)  # Returns 5
mc.incr("counter", 3)  # Returns 8
```

## Testing

The project includes comprehensive unit tests covering:
- Basic set/get/delete operations
- Multi-key operations
- Increment/decrement functionality
- Hash utilities
- Data structure validation
- **Hints system functionality**
- **Collision handling**

Run tests with:
```bash
mvn test
```

## Key Improvements Over Original Implementation

### Hints System Implementation
- **CollisionTable**: Proper hash collision handling
- **HintManager**: Centralized hint management
- **Fast Lookups**: Priority-based lookup system (hints → HTree → data)
- **Collision Resolution**: Automatic detection and handling of hash collisions

### Performance Optimizations
- **Dual Index**: Both hints and HTree maintained for optimal performance
- **Memory Efficiency**: Concurrent data structures for thread safety
- **Lock Optimization**: Read-write locks for better concurrency

## Limitations

This implementation focuses on core functionality and has some limitations compared to the full GoBeansDB:

1. **No Compression**: Compression detection is implemented but actual compression/decompression is not
2. **Simplified GC**: No garbage collection of old data files
3. **No Hint Files**: Index rebuilding from data files not implemented
4. **No Replication**: Single-node operation only
5. **Basic Protocol**: Limited memcached command support
6. **No Persistence**: Index is rebuilt on restart (data files are persistent)

## Contributing

This is a simplified educational implementation. For production use, consider the original [GoBeansDB](https://github.com/douban/gobeansdb) project.

## License

This project follows the same license as the original GoBeansDB project.
