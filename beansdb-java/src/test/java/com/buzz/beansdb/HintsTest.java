package com.buzz.beansdb;

import com.buzz.beansdb.core.*;
import com.buzz.beansdb.store.*;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test cases for Hints functionality
 */
public class HintsTest {
    
    private Bucket bucket;
    private String testHome;
    
    @BeforeEach
    public void setUp() throws IOException {
        // Create temporary directory for testing
        testHome = "/tmp/beansdb-hints-test-" + System.currentTimeMillis();
        bucket = new Bucket(0);
        bucket.open(0, testHome);
    }
    
    @AfterEach
    public void tearDown() throws IOException {
        if (bucket != null) {
            bucket.close();
        }
        
        // Clean up test directory
        if (testHome != null) {
            deleteDirectory(Paths.get(testHome));
        }
    }
    
    @Test
    public void testHintManagerBasicOperations() {
        HintManager hintManager = bucket.getHintManager();
        assertNotNull(hintManager, "HintManager should be initialized");
        
        // Test setting a hint
        KeyInfo keyInfo = new KeyInfo("test_key");
        keyInfo.prepare();
        
        Meta meta = new Meta();
        meta.setVersion(1);
        meta.setValueHash(12345);
        meta.setTimestamp(System.currentTimeMillis() / 1000);
        
        Position position = new Position(0, 1024);
        
        hintManager.set(keyInfo, meta, position, 256, "test");
        
        // Test getting the hint
        HintItem item = hintManager.getItem(keyInfo.getKeyHash(), keyInfo.getStringKey(), false);
        assertNotNull(item, "Hint item should exist");
        assertEquals(keyInfo.getKeyHash(), item.getKeyHash(), "Key hash should match");
        assertEquals(keyInfo.getStringKey(), item.getKey(), "Key should match");
        assertEquals(meta.getVersion(), item.getVersion(), "Version should match");
        assertEquals(meta.getValueHash(), item.getValueHash(), "Value hash should match");
        assertEquals(position, item.getPosition(), "Position should match");
    }
    
    @Test
    public void testCollisionTable() {
        CollisionTable collisionTable = new CollisionTable();
        
        // Create two items with same hash but different keys
        long keyHash = 12345L;
        String key1 = "key1";
        String key2 = "key2";
        
        HintItem item1 = new HintItem(keyHash, new Position(0, 100), 1, 111, key1);
        HintItem item2 = new HintItem(keyHash, new Position(0, 200), 2, 222, key2);
        
        // Set both items
        collisionTable.compareAndSet(item1, "test1");
        collisionTable.compareAndSet(item2, "test2");
        
        // Verify both can be retrieved
        HintItem retrieved1 = collisionTable.get(keyHash, key1);
        HintItem retrieved2 = collisionTable.get(keyHash, key2);
        
        assertNotNull(retrieved1, "First item should exist");
        assertNotNull(retrieved2, "Second item should exist");
        assertEquals(key1, retrieved1.getKey(), "First key should match");
        assertEquals(key2, retrieved2.getKey(), "Second key should match");
        assertEquals(2, collisionTable.size(), "Should have 2 items");
    }
    
    @Test
    public void testBucketWithHints() throws IOException {
        // Test that bucket operations use hints correctly
        String key = "test_key_with_hints";
        String value = "test_value_with_hints";
        
        KeyInfo keyInfo = new KeyInfo(key);
        keyInfo.prepare();
        
        Payload payload = new Payload();
        payload.setBody(value.getBytes());
        payload.getMeta().setVersion(1);
        payload.getMeta().setTimestamp(System.currentTimeMillis() / 1000);
        
        // Set the record (should update both HTree and hints)
        bucket.checkAndSet(keyInfo, payload);
        
        // Verify hint was created
        HintManager hintManager = bucket.getHintManager();
        HintItem hintItem = hintManager.getItem(keyInfo.getKeyHash(), keyInfo.getStringKey(), false);
        assertNotNull(hintItem, "Hint item should be created");
        assertEquals(key, hintItem.getKey(), "Hint key should match");
        
        // Get the record (should use hints for fast lookup)
        Payload retrieved = bucket.get(keyInfo, false);
        assertNotNull(retrieved, "Retrieved payload should not be null");
        assertArrayEquals(value.getBytes(), retrieved.getBody(), "Retrieved value should match");
    }
    
    @Test
    public void testHashCollisionHandling() throws IOException {
        // This test simulates a hash collision scenario
        String key1 = "collision_key_1";
        String key2 = "collision_key_2";
        
        // Force same hash for testing (in real scenario this would be rare)
        KeyInfo keyInfo1 = new KeyInfo(key1);
        KeyInfo keyInfo2 = new KeyInfo(key2);
        keyInfo1.prepare();
        keyInfo2.prepare();
        
        // Manually set same hash to simulate collision
        long sameHash = 999999L;
        keyInfo1.setKeyHash(sameHash);
        keyInfo2.setKeyHash(sameHash);
        
        Payload payload1 = new Payload();
        payload1.setBody("value1".getBytes());
        payload1.getMeta().setVersion(1);
        
        Payload payload2 = new Payload();
        payload2.setBody("value2".getBytes());
        payload2.getMeta().setVersion(2);
        
        // Set both records
        bucket.checkAndSet(keyInfo1, payload1);
        bucket.checkAndSet(keyInfo2, payload2);
        
        // Verify both can be retrieved correctly
        Payload retrieved1 = bucket.get(keyInfo1, false);
        Payload retrieved2 = bucket.get(keyInfo2, false);
        
        assertNotNull(retrieved1, "First record should be retrievable");
        assertNotNull(retrieved2, "Second record should be retrievable");
        
        // Note: In a real collision scenario, the collision table should help distinguish
        // between the two keys even though they have the same hash
    }
    
    @Test
    public void testMemOnlyGet() throws IOException {
        String key = "mem_only_test_key";
        String value = "mem_only_test_value";
        
        KeyInfo keyInfo = new KeyInfo(key);
        keyInfo.prepare();
        
        Payload payload = new Payload();
        payload.setBody(value.getBytes());
        payload.getMeta().setVersion(1);
        payload.getMeta().setValueHash(12345);
        payload.getMeta().setTimestamp(System.currentTimeMillis() / 1000);
        
        // Set the record
        bucket.checkAndSet(keyInfo, payload);
        
        // Get with memOnly=true (should return metadata only)
        Payload memOnlyResult = bucket.get(keyInfo, true);
        assertNotNull(memOnlyResult, "Memory-only result should not be null");
        assertEquals(1, memOnlyResult.getMeta().getVersion(), "Version should match");
        // Body might be empty or minimal for memory-only access
    }
    
    @Test
    public void testHintStats() {
        HintManager hintManager = bucket.getHintManager();
        
        // Initially should have no items
        HintManager.HintStats stats = hintManager.getStats();
        assertEquals(0, stats.getTotalItems(), "Should start with 0 items");
        
        // Add some items
        for (int i = 0; i < 5; i++) {
            KeyInfo keyInfo = new KeyInfo("key" + i);
            keyInfo.prepare();
            
            Meta meta = new Meta();
            meta.setVersion(i + 1);
            meta.setValueHash(i * 100);
            
            Position position = new Position(0, i * 256);
            hintManager.set(keyInfo, meta, position, 256, "test");
        }
        
        stats = hintManager.getStats();
        assertEquals(5, stats.getTotalItems(), "Should have 5 items");
    }
    
    private void deleteDirectory(Path directory) throws IOException {
        if (Files.exists(directory)) {
            Files.walk(directory)
                    .sorted((a, b) -> b.compareTo(a)) // Reverse order to delete files before directories
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            // Ignore deletion errors in tests
                        }
                    });
        }
    }
}
