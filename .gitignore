# Compiled Object files, Static and Dynamic libs (Shared Objects)
*.o
*.a
*.so
*.py[co]

# Folders
.idea/
_obj
_test
unittest.xml
testdb/
src/gobeansdb/gobeansdb
src/gopkg.in/
bin/
src/github.com/
venv/
set-env.sh

# Architecture specific extensions/prefixes
*.[568vq]
[568vq].out

*.cgo1.go
*.cgo2.c
_cgo_defun.c
_cgo_gotypes.go
_cgo_export.*

_testmain.go

*.exe
*.test
*.prof
*.log

.tag*
main
vendor/
